import { useState, useCallback, useMemo } from 'react';
import { DollarSign, KeyRound, WalletCards } from 'lucide-react';
import { TransactionType } from '@/lib/api/services/fetchProperty';

interface TransactionTypeOption {
  id: string;
  label: string;
  icon: any;
}

interface TransactionTypeState {
  selectedTransactionType: string;
  isTransactionTypeOpen: boolean;
  transactionTypes: TransactionTypeOption[];
  setSelectedTransactionType: (type: string) => void;
  setIsTransactionTypeOpen: (open: boolean) => void;
  handleTransactionTypeChange: (value: string) => void;
  handleApplyTransactionType: (onFilterChange: (key: string, value: string) => void) => void;
  resetTransactionType: () => void;
  getTransactionTypeDisplayText: () => string;
  isRentSelected: boolean;
  isSaleSelected: boolean;
}

/**
 * Hook quản lý logic transaction type selection
 * Hỗ trợ bán/thuê/tất cả với logic apply/reset
 */
export function useTransactionType(): TransactionTypeState {
  const [selectedTransactionType, setSelectedTransactionType] = useState<string>('');
  const [isTransactionTypeOpen, setIsTransactionTypeOpen] = useState(false);

  // Transaction types configuration - synced with SearchFilter.tsx
  const transactionTypes = useMemo(
    (): TransactionTypeOption[] => [
      { id: 'both', label: 'Tất cả', icon: WalletCards },
      { id: TransactionType.FOR_SALE, label: 'Bán', icon: DollarSign },
      { id: TransactionType.FOR_RENT, label: 'Cho thuê', icon: KeyRound },
    ],
    []
  );

  // Computed properties for easier checking
  const isRentSelected = useMemo(() => {
    return selectedTransactionType === TransactionType.FOR_RENT;
  }, [selectedTransactionType]);

  const isSaleSelected = useMemo(() => {
    return selectedTransactionType === TransactionType.FOR_SALE;
  }, [selectedTransactionType]);

  // Handle transaction type change
  const handleTransactionTypeChange = useCallback((value: string) => {
    setSelectedTransactionType(value);
  }, []);

  // Apply transaction type to filters
  const handleApplyTransactionType = useCallback(
    (onFilterChange: (key: string, value: string) => void) => {
      // If 'both' is selected, clear the filter (empty string means all)
      const filterValue = selectedTransactionType === 'both' ? '' : selectedTransactionType;
      onFilterChange('transactionType', filterValue);
      setIsTransactionTypeOpen(false);
    },
    [selectedTransactionType]
  );

  // Reset transaction type selection
  const resetTransactionType = useCallback(() => {
    setSelectedTransactionType('');
    setIsTransactionTypeOpen(false);
  }, []);

  // Get display text for transaction type button
  const getTransactionTypeDisplayText = useCallback(() => {
    if (!selectedTransactionType) {
      return 'Mua/Thuê';
    }

    const selectedOption = transactionTypes.find(type => type.id === selectedTransactionType);
    return selectedOption?.label || 'Mua/Thuê';
  }, [selectedTransactionType, transactionTypes]);

  // Sync selected type from filter value
  const syncFromFilterValue = useCallback((filterValue: string) => {
    if (!filterValue) {
      setSelectedTransactionType('both'); // Default to 'both' when no filter
      return;
    }

    setSelectedTransactionType(filterValue);
  }, []);

  // Get display text based on filter value (for button display)
  const getDisplayTextFromFilter = useCallback((filterValue: string) => {
    if (!filterValue) {
      return 'Mua/Thuê';
    }

    if (filterValue === TransactionType.FOR_SALE) {
      return 'Bán';
    }

    if (filterValue === TransactionType.FOR_RENT) {
      return 'Thuê';
    }

    return 'Mua/Thuê';
  }, []);

  return {
    selectedTransactionType,
    isTransactionTypeOpen,
    transactionTypes,
    setSelectedTransactionType,
    setIsTransactionTypeOpen,
    handleTransactionTypeChange,
    handleApplyTransactionType,
    resetTransactionType,
    getTransactionTypeDisplayText,
    isRentSelected,
    isSaleSelected,
    // Additional utility methods
    syncFromFilterValue,
    getDisplayTextFromFilter,
  } as TransactionTypeState & {
    syncFromFilterValue: (filterValue: string) => void;
    getDisplayTextFromFilter: (filterValue: string) => string;
  };
}
