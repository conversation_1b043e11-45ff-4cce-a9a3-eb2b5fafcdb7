import { useState, useCallback, useMemo } from 'react';

interface PropertySizeState {
  sizeSliderValue: [number, number];
  minSize: string;
  maxSize: string;
  isPropertySizeOpen: boolean;
  setSizeSliderValue: (value: [number, number]) => void;
  setMinSize: (size: string) => void;
  setMaxSize: (size: string) => void;
  setIsPropertySizeOpen: (open: boolean) => void;
  resetPropertySize: () => void;
  formatSizeLabel: (value: number, isMax?: boolean) => string;
  formatSizeDisplay: (sizeRange: string) => string;
  getSizeRangeString: () => string;
  handleApplyPropertySize: (onFilterChange: (key: string, value: string) => void) => void;
  handleMinSizeChange: (value: string) => void;
  handleMaxSizeChange: (value: string) => void;
  handleMinSizeBlur: () => void;
  handleMaxSizeBlur: () => void;
  handleMinSizeFocus: () => void;
  handleMaxSizeFocus: () => void;
  handleQuickSizeRangeSelect: (range: [number, number]) => void;
  sizeRanges: Array<{
    label: string;
    value: [number, number];
  }>;
  sizeDistribution: Array<{
    size: string;
    count: number;
  }>;
}

/**
 * Hook quản lý logic property size slider và formatting
 * Tương tự usePriceRange nhưng cho diện tích bất động sản với URL sync và input handling
 */
export function usePropertySize(): PropertySizeState {
  const [minSize, setMinSize] = useState('');
  const [maxSize, setMaxSize] = useState('');
  const [sizeSliderValue, setSizeSliderValue] = useState<[number, number]>([0, 1000]);
  const [isPropertySizeOpen, setIsPropertySizeOpen] = useState(false);

  // Predefined size ranges - synced with SearchFilter.tsx
  const sizeRanges = useMemo(
    () => [
      { label: 'Dưới 30m²', value: [0, 30] as [number, number] },
      { label: '30-50m²', value: [30, 50] as [number, number] },
      { label: '50-70m²', value: [50, 70] as [number, number] },
      { label: '70-100m²', value: [70, 100] as [number, number] },
      { label: '100-150m²', value: [100, 150] as [number, number] },
      { label: 'Trên 150m²', value: [150, 1000] as [number, number] },
    ],
    []
  );

  // Size distribution data - synced with SearchFilter.tsx
  const sizeDistribution = useMemo(
    () => [
      { size: '0-30', count: 120 },
      { size: '30-50', count: 280 },
      { size: '50-70', count: 320 },
      { size: '70-100', count: 250 },
      { size: '100-150', count: 180 },
      { size: '150-200', count: 120 },
      { size: '200-300', count: 80 },
      { size: '300-500', count: 50 },
      { size: '500-1000', count: 30 },
      { size: '1000+', count: 20 },
    ],
    []
  );

  // Format size label for slider
  const formatSizeLabel = useCallback((value: number, isMax: boolean = false) => {
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)} nghìn${isMax ? '+' : ''} m²`;
    }
    return `${value}${isMax ? '+' : ''} m²`;
  }, []);

  // Format size display for button
  const formatSizeDisplay = useCallback((sizeRange: string) => {
    if (!sizeRange) return 'Diện tích';
    return sizeRange;
  }, []);

  // Get size range string for filters
  const getSizeRangeString = useCallback(() => {
    if (sizeSliderValue[0] === 0 && sizeSliderValue[1] === 1000) {
      return '';
    }
    return `${formatSizeLabel(sizeSliderValue[0])} đến ${formatSizeLabel(sizeSliderValue[1])}`;
  }, [sizeSliderValue, formatSizeLabel]);

  // Reset size range
  const resetPropertySize = useCallback(() => {
    setSizeSliderValue([0, 1000]);
    setMinSize('');
    setMaxSize('');
  }, []);

  // Handle size input changes with validation
  const handleMinSizeChange = useCallback((value: string) => {
    const rawValue = value.replace(/[^0-9]/g, '');
    setMinSize(rawValue);
    setSizeSliderValue(prev => [Number(rawValue) || 0, prev[1]]);
  }, []);

  const handleMaxSizeChange = useCallback((value: string) => {
    const rawValue = value.replace(/[^0-9]/g, '');
    setMaxSize(rawValue);
    setSizeSliderValue(prev => [prev[0], Number(rawValue) || 1000]);
  }, []);

  // Handle input blur events (format display)
  const handleMinSizeBlur = useCallback(() => {
    if (minSize) {
      setMinSize(formatSizeLabel(Number(minSize)));
    }
  }, [minSize, formatSizeLabel]);

  const handleMaxSizeBlur = useCallback(() => {
    if (maxSize) {
      setMaxSize(formatSizeLabel(Number(maxSize)));
    }
  }, [maxSize, formatSizeLabel]);

  // Handle input focus events (show raw numbers)
  const handleMinSizeFocus = useCallback(() => {
    setMinSize(sizeSliderValue[0]?.toString() || '');
  }, [sizeSliderValue]);

  const handleMaxSizeFocus = useCallback(() => {
    setMaxSize(sizeSliderValue[1]?.toString() || '');
  }, [sizeSliderValue]);

  // Handle quick range selection
  const handleQuickSizeRangeSelect = useCallback((range: [number, number]) => {
    setSizeSliderValue(range);
    setMinSize(range[0].toString());
    setMaxSize(range[1].toString());
  }, []);

  // Apply property size to filters
  const handleApplyPropertySize = useCallback(
    (onFilterChange: (key: string, value: string) => void) => {
      const sizeRangeString = getSizeRangeString();
      onFilterChange('propertySize', sizeRangeString);
      setIsPropertySizeOpen(false);
    },
    [getSizeRangeString]
  );

  // Sync from URL parameters
  const syncFromURLParams = useCallback((minAreaParam: string, maxAreaParam: string) => {
    const minArea = Number(minAreaParam) || 0;
    const maxArea = Number(maxAreaParam) || 1000;

    setSizeSliderValue([minArea, maxArea]);
    setMinSize(minArea.toString());
    setMaxSize(maxArea.toString());
  }, []);

  return {
    sizeSliderValue,
    minSize,
    maxSize,
    isPropertySizeOpen,
    setSizeSliderValue,
    setMinSize,
    setMaxSize,
    setIsPropertySizeOpen,
    resetPropertySize,
    formatSizeLabel,
    formatSizeDisplay,
    getSizeRangeString,
    handleApplyPropertySize,
    handleMinSizeChange,
    handleMaxSizeChange,
    handleMinSizeBlur,
    handleMaxSizeBlur,
    handleMinSizeFocus,
    handleMaxSizeFocus,
    handleQuickSizeRangeSelect,
    sizeRanges,
    sizeDistribution,
    // Additional utility method
    syncFromURLParams,
  } as PropertySizeState & {
    syncFromURLParams: (minAreaParam: string, maxAreaParam: string) => void;
  };
}
