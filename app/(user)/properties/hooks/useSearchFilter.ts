import { useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { TransactionType } from '@/lib/api/services/fetchProperty';

// Import all the specialized hooks
import { useFilterState } from './useFilterState';
import { useLocationData } from './useLocationData';
import { usePriceRange } from './usePriceRange';
import { usePropertySize } from './usePropertySize';
import { usePropertyTypes } from './usePropertyTypes';
import { useTransactionType } from './useTransactionType';
import { useRoomFilters } from './useRoomFilters';

interface SearchFilterState {
  // Filter state
  filters: ReturnType<typeof useFilterState>['filters'];
  searchQuery: string;
  isSearching: boolean;

  // Location
  locationData: ReturnType<typeof useLocationData>;

  // Price range
  priceRange: ReturnType<typeof usePriceRange>;

  // Property size
  propertySize: ReturnType<typeof usePropertySize>;

  // Property types
  propertyTypes: ReturnType<typeof usePropertyTypes>;

  // Transaction type
  transactionType: ReturnType<typeof useTransactionType>;

  // Room filters
  roomFilters: ReturnType<typeof useRoomFilters>;

  // Actions
  handleSearch: () => void;
  clearFilters: () => void;
  countActiveFilters: () => number;
  setSearchQuery: (query: string) => void;
  setIsSearching: (searching: boolean) => void;

  // Dropdown states
  dropdownStates: ReturnType<typeof useFilterState>['dropdownStates'];
  toggleDropdown: ReturnType<typeof useFilterState>['toggleDropdown'];
}

/**
 * Hook tổng hợp quản lý toàn bộ search filter logic
 * Kết hợp tất cả các hooks con và đồng bộ hóa state
 */
export function useSearchFilter(): SearchFilterState {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize all hooks
  const filterState = useFilterState();
  const locationData = useLocationData();

  // Get transaction type from filters for price range context
  const currentTransactionType = filterState.filters.transactionType as TransactionType;

  const priceRange = usePriceRange(currentTransactionType);
  const propertySize = usePropertySize();
  const propertyTypes = usePropertyTypes();
  const transactionType = useTransactionType();
  const roomFilters = useRoomFilters();

  // Sync all hooks from URL parameters
  const syncFromURL = useCallback(() => {
    const params = new URLSearchParams(searchParams);

    // Sync search query
    if (params.has('searchTerm')) {
      filterState.setSearchQuery(params.get('searchTerm') || '');
    } else {
      filterState.setSearchQuery('');
    }

    // Sync location filters
    const locationFilters = {
      city: params.get('city') || undefined,
      district: params.get('district') || undefined,
      ward: params.get('ward') || undefined,
    };

    // Sync property type
    if (params.has('propertyType')) {
      const propertyTypeValue = params.get('propertyType') || '';
      (propertyTypes as any).syncFromFilterValue(propertyTypeValue);
      filterState.updateFilter('propertyType', propertyTypeValue);
    } else {
      (propertyTypes as any).syncFromFilterValue('');
      filterState.updateFilter('propertyType', '');
    }

    // Sync transaction type
    if (params.has('transactionType')) {
      const transactionTypeValue = params.get('transactionType') || '';
      (transactionType as any).syncFromFilterValue(transactionTypeValue);
      filterState.updateFilter('transactionType', transactionTypeValue);
    } else {
      (transactionType as any).syncFromFilterValue('');
      filterState.updateFilter('transactionType', '');
    }

    // Sync price range
    if (params.has('minPrice') || params.has('maxPrice')) {
      const minPrice = params.get('minPrice') || '0';
      const maxPrice = params.get('maxPrice') || '';
      (priceRange as any).syncFromURLParams(minPrice, maxPrice);

      // Update filter display
      const priceRangeString = priceRange.getPriceRangeString();
      if (priceRangeString) {
        filterState.updateFilter('priceRange', priceRangeString);
      }
    }

    // Sync property size
    if (params.has('minArea') || params.has('maxArea')) {
      const minArea = params.get('minArea') || '0';
      const maxArea = params.get('maxArea') || '1000';
      (propertySize as any).syncFromURLParams(minArea, maxArea);

      // Update filter display
      const sizeRangeString = propertySize.getSizeRangeString();
      if (sizeRangeString) {
        filterState.updateFilter('propertySize', sizeRangeString);
      }
    }

    // Sync room filters
    const roomFilterValues = {
      bedCount: params.get('bedCount') || undefined,
      bathCount: params.get('bathCount') || undefined,
      exactBedMatch: params.get('exactBedMatch') || undefined,
    };

    (roomFilters as any).syncFromFilterValues(roomFilterValues);

    // Update display filters
    if (roomFilterValues.bedCount) {
      const bedDisplayText =
        roomFilterValues.bedCount === 'studio'
          ? 'Studio'
          : `${roomFilterValues.bedCount} phòng ngủ`;
      filterState.updateFilter('bedCountDisplay', bedDisplayText);
    }

    if (roomFilterValues.bathCount) {
      const bathDisplayText = `${roomFilterValues.bathCount} phòng tắm`;
      filterState.updateFilter('bathCountDisplay', bathDisplayText);
    }

    // Sync location data (this will trigger API calls if needed)
    (locationData as any).syncFromFilterValues(locationFilters);

    // Update location filters
    Object.entries(locationFilters).forEach(([key, value]) => {
      if (value) {
        filterState.updateFilter(key, value);
      }
    });
  }, [
    searchParams,
    filterState,
    locationData,
    priceRange,
    propertySize,
    propertyTypes,
    transactionType,
    roomFilters,
  ]);

  // Enhanced search handler that builds complete URL
  const handleSearch = useCallback(() => {
    filterState.setIsSearching(true);

    const queryParams = new URLSearchParams();

    // Add search term
    if (filterState.searchQuery.trim()) {
      queryParams.set('searchTerm', filterState.searchQuery.trim());
    }

    // Add location parameters
    if (filterState.filters.city) queryParams.set('city', filterState.filters.city);
    if (filterState.filters.district) queryParams.set('district', filterState.filters.district);
    if (filterState.filters.ward) queryParams.set('ward', filterState.filters.ward);

    // Add property type
    if (filterState.filters.propertyType) {
      queryParams.set('propertyType', filterState.filters.propertyType);
    }

    // Add transaction type
    if (filterState.filters.transactionType) {
      queryParams.set('transactionType', filterState.filters.transactionType);
    }

    // Add price range
    const currentPriceRanges = priceRange.getCurrentPriceRanges();
    if (priceRange.sliderValue[0] > 0 || priceRange.sliderValue[1] < currentPriceRanges.maxValue) {
      queryParams.set('minPrice', priceRange.sliderValue[0].toString());
      queryParams.set('maxPrice', priceRange.sliderValue[1].toString());
    }

    // Add area range
    if (propertySize.sizeSliderValue[0] > 0 || propertySize.sizeSliderValue[1] < 1000) {
      queryParams.set('minArea', propertySize.sizeSliderValue[0].toString());
      queryParams.set('maxArea', propertySize.sizeSliderValue[1].toString());
    }

    // Add room filters
    if (filterState.filters.bedCount) {
      queryParams.set('bedCount', filterState.filters.bedCount);
      if (filterState.filters.exactBedMatch) {
        queryParams.set('exactBedMatch', filterState.filters.exactBedMatch);
      }
    }

    if (filterState.filters.bathCount) {
      queryParams.set('bathCount', filterState.filters.bathCount);
    }

    // Navigate with all parameters
    router.push(`/properties?${queryParams.toString()}`);

    // Reset searching state
    setTimeout(() => {
      filterState.setIsSearching(false);
    }, 500);
  }, [filterState, priceRange, propertySize, router]);

  // Enhanced clear filters
  const clearFilters = useCallback(() => {
    // Reset all hooks
    filterState.clearFilters();
    locationData.resetLocation();
    priceRange.resetPriceRange();
    propertySize.resetPropertySize();
    propertyTypes.resetPropertyTypes();
    transactionType.resetTransactionType();
    roomFilters.resetRoomFilters();

    // Navigate to clean URL
    router.push('/properties');
  }, [
    filterState,
    locationData,
    priceRange,
    propertySize,
    propertyTypes,
    transactionType,
    roomFilters,
    router,
  ]);

  // Sync from URL on mount and when searchParams change
  useEffect(() => {
    syncFromURL();
  }, [syncFromURL]);

  return {
    // State
    filters: filterState.filters,
    searchQuery: filterState.searchQuery,
    isSearching: filterState.isSearching,
    dropdownStates: filterState.dropdownStates,

    // Specialized hooks
    locationData,
    priceRange,
    propertySize,
    propertyTypes,
    transactionType,
    roomFilters,

    // Actions
    handleSearch,
    clearFilters,
    countActiveFilters: filterState.countActiveFilters,
    setSearchQuery: filterState.setSearchQuery,
    setIsSearching: filterState.setIsSearching,
    toggleDropdown: filterState.toggleDropdown,
  };
}
