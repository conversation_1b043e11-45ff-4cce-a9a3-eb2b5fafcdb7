import { useState, useCallback, useMemo } from 'react';

interface RoomOption {
  id: string;
  label: string;
}

interface RoomFiltersState {
  selectedBedCount: string;
  selectedBathCount: string;
  isExactBedMatch: boolean;
  isRoomsDropdownOpen: boolean;
  bedOptions: RoomOption[];
  exactBedOptions: RoomOption[];
  bathOptions: RoomOption[];
  setSelectedBedCount: (count: string) => void;
  setSelectedBathCount: (count: string) => void;
  setIsExactBedMatch: (exact: boolean) => void;
  setIsRoomsDropdownOpen: (open: boolean) => void;
  handleBedCountChange: (value: string) => void;
  handleBathCountChange: (value: string) => void;
  handleApplyRooms: (onFilterChange: (key: string, value: string) => void) => void;
  resetRoomFilters: () => void;
  getRoomDisplayText: () => string;
  getCurrentBedOptions: () => RoomOption[];
}

/**
 * Hook quản lý logic bed/bath count filters
 * Hỗ trợ exact match option và display text formatting
 */
export function useRoomFilters(): RoomFiltersState {
  const [selectedBedCount, setSelectedBedCount] = useState<string>('any');
  const [selectedBathCount, setSelectedBathCount] = useState<string>('any');
  const [isExactBedMatch, setIsExactBedMatch] = useState(false);
  const [isRoomsDropdownOpen, setIsRoomsDropdownOpen] = useState(false);

  // Room options configuration - synced with SearchFilter.tsx
  const bedOptions = useMemo(
    (): RoomOption[] => [
      { id: 'any', label: 'Bất kỳ' },
      { id: '1+', label: '1+' },
      { id: '2+', label: '2+' },
      { id: '3+', label: '3+' },
      { id: '4+', label: '4+' },
      { id: '5+', label: '5+' },
    ],
    []
  );

  const exactBedOptions = useMemo(
    (): RoomOption[] => [
      { id: 'studio', label: 'Studio' },
      { id: '1', label: '1' },
      { id: '2', label: '2' },
      { id: '3', label: '3' },
      { id: '4', label: '4' },
      { id: '5', label: '5' },
    ],
    []
  );

  const bathOptions = useMemo(
    (): RoomOption[] => [
      { id: 'any', label: 'Bất kỳ' },
      { id: '1+', label: '1+' },
      { id: '2+', label: '2+' },
      { id: '3+', label: '3+' },
      { id: '4+', label: '4+' },
      { id: '5+', label: '5+' },
    ],
    []
  );

  // Get current bed options based on exact match setting
  const getCurrentBedOptions = useCallback(() => {
    return isExactBedMatch ? exactBedOptions : bedOptions;
  }, [isExactBedMatch, exactBedOptions, bedOptions]);

  // Handle bed count change
  const handleBedCountChange = useCallback((value: string) => {
    setSelectedBedCount(value);
  }, []);

  // Handle bath count change
  const handleBathCountChange = useCallback((value: string) => {
    setSelectedBathCount(value);
  }, []);

  // Apply room filters
  const handleApplyRooms = useCallback(
    (onFilterChange: (key: string, value: string) => void) => {
      // Handle bed count
      const bedValue = selectedBedCount === 'any' ? '' : selectedBedCount;

      let bedDisplayText = '';
      if (selectedBedCount !== 'any') {
        if (isExactBedMatch) {
          bedDisplayText =
            selectedBedCount === 'studio' ? 'Studio' : `${selectedBedCount} phòng ngủ`;
        } else {
          bedDisplayText = `${selectedBedCount} phòng ngủ`;
        }
      }

      // Handle bath count
      const bathValue = selectedBathCount === 'any' ? '' : selectedBathCount;
      const bathDisplayText = selectedBathCount === 'any' ? '' : `${selectedBathCount} phòng tắm`;

      // Update filters
      onFilterChange('bedCount', bedValue);
      onFilterChange('exactBedMatch', isExactBedMatch.toString());
      onFilterChange('bedCountDisplay', bedDisplayText);
      onFilterChange('bathCount', bathValue);
      onFilterChange('bathCountDisplay', bathDisplayText);

      setIsRoomsDropdownOpen(false);
    },
    [selectedBedCount, selectedBathCount, isExactBedMatch]
  );

  // Reset room filters
  const resetRoomFilters = useCallback(() => {
    setSelectedBedCount('any');
    setSelectedBathCount('any');
    setIsExactBedMatch(false);
    setIsRoomsDropdownOpen(false);
  }, []);

  // Get display text for room button
  const getRoomDisplayText = useCallback((bedCountDisplay?: string, bathCountDisplay?: string) => {
    if (bedCountDisplay && bathCountDisplay) {
      return `${bedCountDisplay}, ${bathCountDisplay}`;
    }

    if (bedCountDisplay) {
      return bedCountDisplay;
    }

    if (bathCountDisplay) {
      return bathCountDisplay;
    }

    return 'Số phòng';
  }, []);

  // Sync from filter values
  const syncFromFilterValues = useCallback(
    (filters: {
      bedCount?: string;
      bathCount?: string;
      exactBedMatch?: string;
      bedCountDisplay?: string;
      bathCountDisplay?: string;
    }) => {
      if (filters.bedCount) {
        setSelectedBedCount(filters.bedCount);
      } else {
        setSelectedBedCount('any');
      }

      if (filters.bathCount) {
        setSelectedBathCount(filters.bathCount);
      } else {
        setSelectedBathCount('any');
      }

      if (filters.exactBedMatch) {
        setIsExactBedMatch(filters.exactBedMatch === 'true');
      } else {
        setIsExactBedMatch(false);
      }
    },
    []
  );

  return {
    selectedBedCount,
    selectedBathCount,
    isExactBedMatch,
    isRoomsDropdownOpen,
    bedOptions,
    exactBedOptions,
    bathOptions,
    setSelectedBedCount,
    setSelectedBathCount,
    setIsExactBedMatch,
    setIsRoomsDropdownOpen,
    handleBedCountChange,
    handleBathCountChange,
    handleApplyRooms,
    resetRoomFilters,
    getRoomDisplayText,
    getCurrentBedOptions,
    // Additional utility method
    syncFromFilterValues,
  } as RoomFiltersState & {
    syncFromFilterValues: (filters: {
      bedCount?: string;
      bathCount?: string;
      exactBedMatch?: string;
      bedCountDisplay?: string;
      bathCountDisplay?: string;
    }) => void;
  };
}
